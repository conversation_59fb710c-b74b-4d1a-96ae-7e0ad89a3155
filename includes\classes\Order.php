<?php
/**
 * Order Class
 *
 * Handles order-related operations
 */
class Order {
    private $db;
    private $table = 'orders';

    /**
     * Constructor
     */
    public function __construct() {
        $this->db = Database::getInstance();
    }

    /**
     * Get all orders
     *
     * @param array $options
     * @return array
     */
    public function getAll($options = []) {
        $sql = "SELECT o.*, u.first_name, u.last_name, u.email,
                       sa.address_line1 as shipping_address,
                       sa.city as shipping_city,
                       l.first_name as livreur_first_name,
                       l.last_name as livreur_last_name
                FROM {$this->table} o
                LEFT JOIN users u ON o.user_id = u.id
                LEFT JOIN addresses sa ON o.shipping_address_id = sa.id
                LEFT JOIN users l ON o.livreur_id = l.id";

        $params = [];
        $conditions = [];

        if (isset($options['status'])) {
            $conditions[] = "o.status = :status";
            $params['status'] = $options['status'];
        }

        if (isset($options['livreur_id'])) {
            $conditions[] = "o.livreur_id = :livreur_id";
            $params['livreur_id'] = $options['livreur_id'];
        }

        if (!empty($conditions)) {
            $sql .= " WHERE " . implode(" AND ", $conditions);
        }

        $sql .= " ORDER BY o.created_at DESC";

        if (isset($options['limit'])) {
            $sql .= " LIMIT " . (int)$options['limit'];
        }

        return $this->db->fetchAll($sql, $params);
    }

    /**
     * Get pending orders
     *
     * @return array
     */
    public function getPending() {
        return $this->getAll(['status' => 'pending']);
    }

    /**
     * Get orders by livreur
     *
     * @param int $livreurId
     * @return array
     */
    public function getByLivreur($livreurId) {
        return $this->getAll(['livreur_id' => $livreurId]);
    }

    /**
     * Get delivered orders by livreur
     *
     * @param int $livreurId
     * @return array
     */
    public function getDeliveredByLivreur($livreurId) {
        return $this->getAll(['livreur_id' => $livreurId, 'status' => 'delivered']);
    }

    /**
     * Assign order to livreur
     *
     * @param int $orderId
     * @param int $livreurId
     * @return bool
     */
    public function assignToLivreur($orderId, $livreurId) {
        return $this->db->update(
            $this->table,
            ['livreur_id' => $livreurId, 'status' => 'shipped'],
            'id = :id',
            ['id' => $orderId]
        );
    }

    /**
     * Mark order as delivered
     *
     * @param int $orderId
     * @param int $livreurId
     * @return bool
     */
    public function markAsDelivered($orderId, $livreurId) {
        return $this->db->update(
            $this->table,
            [
                'status' => 'delivered',
                'delivered_at' => date('Y-m-d H:i:s')
            ],
            'id = :id AND livreur_id = :livreur_id',
            ['id' => $orderId, 'livreur_id' => $livreurId]
        );
    }

    /**
     * Get order by ID
     *
     * @param int $id
     * @return array|false
     */
    public function getById($id) {
        $orders = $this->getAll();
        foreach ($orders as $order) {
            if ($order['id'] == $id) {
                return $order;
            }
        }
        return false;
    }

    /**
     * Get order items
     *
     * @param int $orderId
     * @return array
     */
    public function getOrderItems($orderId) {
        return $this->db->fetchAll(
            "SELECT oi.*, p.name, p.image
             FROM order_items oi
             LEFT JOIN products p ON oi.product_id = p.id
             WHERE oi.order_id = :order_id",
            ['order_id' => $orderId]
        );
    }

    /**
     * Update order status
     *
     * @param int $orderId
     * @param string $status
     * @return bool
     */
    public function updateStatus($orderId, $status) {
        $data = ['status' => $status];

        if ($status === 'delivered') {
            $data['delivered_at'] = date('Y-m-d H:i:s');
        }

        return $this->db->update(
            $this->table,
            $data,
            'id = :id',
            ['id' => $orderId]
        );
    }

    /**
     * Create new order
     *
     * @param array $orderData
     * @return int|false Order ID or false on failure
     */
    public function create($orderData) {
        // Generate order number
        $orderData['order_number'] = 'NB' . date('Ymd') . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);

        return $this->db->insert($this->table, $orderData);
    }

    /**
     * Delete order
     *
     * @param int $orderId
     * @return bool
     */
    public function delete($orderId) {
        return $this->db->delete(
            $this->table,
            'id = :id',
            ['id' => $orderId]
        );
    }

    /**
     * Get all orders for a specific user
     *
     * @param int $userId
     * @param int $limit
     * @param int $offset
     * @return array
     */
    public function getUserOrders($userId, $limit = null, $offset = 0) {
        $sql = "SELECT o.*,
                       sa.address_line1 as shipping_address_line1,
                       sa.city as shipping_city,
                       sa.postal_code as shipping_postal_code,
                       sa.country as shipping_country
                FROM {$this->table} o
                LEFT JOIN addresses sa ON o.shipping_address_id = sa.id
                WHERE o.user_id = :user_id
                ORDER BY o.created_at DESC";

        if ($limit) {
            $sql .= " LIMIT :limit OFFSET :offset";
        }

        $params = ['user_id' => $userId];
        if ($limit) {
            $params['limit'] = $limit;
            $params['offset'] = $offset;
        }

        return $this->db->fetchAll($sql, $params);
    }

    /**
     * Get order by ID for a specific user
     *
     * @param int $orderId
     * @param int $userId
     * @return array|false
     */
    public function getUserOrder($orderId, $userId) {
        return $this->db->fetchOne(
            "SELECT o.*,
                    sa.address_line1 as shipping_address_line1,
                    sa.city as shipping_city,
                    sa.postal_code as shipping_postal_code,
                    sa.country as shipping_country,
                    ba.address_line1 as billing_address_line1,
                    ba.city as billing_city,
                    ba.postal_code as billing_postal_code,
                    ba.country as billing_country
             FROM {$this->table} o
             LEFT JOIN addresses sa ON o.shipping_address_id = sa.id
             LEFT JOIN addresses ba ON o.billing_address_id = ba.id
             WHERE o.id = :id AND o.user_id = :user_id",
            ['id' => $orderId, 'user_id' => $userId]
        );
    }

    /**
     * Get orders with their items for a user
     *
     * @param int $userId
     * @param int $limit
     * @return array
     */
    public function getUserOrdersWithItems($userId, $limit = null) {
        $orders = $this->getUserOrders($userId, $limit);

        foreach ($orders as &$order) {
            $order['items'] = $this->getOrderItems($order['id']);
        }

        return $orders;
    }

    /**
     * Get order count for a user
     *
     * @param int $userId
     * @return int
     */
    public function getUserOrderCount($userId) {
        $result = $this->db->fetchOne(
            "SELECT COUNT(*) as count FROM {$this->table} WHERE user_id = :user_id",
            ['user_id' => $userId]
        );

        return $result ? (int)$result['count'] : 0;
    }

    /**
     * Format order status for display
     *
     * @param string $status
     * @return array
     */
    public function formatStatus($status) {
        $statusMap = [
            'pending' => ['label' => 'En attente', 'class' => 'status-pending'],
            'processing' => ['label' => 'En traitement', 'class' => 'status-processing'],
            'shipped' => ['label' => 'Expédiée', 'class' => 'status-shipped'],
            'delivered' => ['label' => 'Livrée', 'class' => 'status-delivered'],
            'cancelled' => ['label' => 'Annulée', 'class' => 'status-cancelled']
        ];

        return $statusMap[$status] ?? ['label' => ucfirst($status), 'class' => 'status-unknown'];
    }

    /**
     * Format order date for display
     *
     * @param string $date
     * @return string
     */
    public function formatDate($date) {
        return date('d/m/Y', strtotime($date));
    }

    /**
     * Format price for display
     *
     * @param float $price
     * @return string
     */
    public function formatPrice($price) {
        return number_format($price, 2) . ' €';
    }
}
