/**
 * Nuit Blanche - Profile Page JavaScript
 */

document.addEventListener('DOMContentLoaded', function() {
    // Tab navigation with buttons
    const tabButtons = document.querySelectorAll('.profile-tab-btn');
    const tabs = document.querySelectorAll('.profile-tab');

    tabButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();

            // Get target tab ID from data-tab attribute
            const targetId = this.getAttribute('data-tab');

            // Remove active class from all buttons and tabs
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabs.forEach(tab => tab.classList.remove('active'));

            // Add active class to clicked button and corresponding tab
            this.classList.add('active');
            const targetTab = document.getElementById(targetId);
            if (targetTab) {
                targetTab.classList.add('active');

                // Check for empty states when switching tabs
                checkEmptyStates(targetId);
            }
        });
    });

    // Function to check and handle empty states
    function checkEmptyStates(tabId) {
        switch(tabId) {
            case 'orders':
                checkOrdersEmpty();
                break;
            case 'addresses':
                checkAddressesEmpty();
                break;
            case 'wishlist':
                checkWishlistEmpty();
                break;
        }
    }

    function checkOrdersEmpty() {
        const ordersList = document.querySelector('.orders-list');
        const emptyState = document.querySelector('#orders .empty-state');
        const orderItems = document.querySelectorAll('.order-item');

        if (orderItems.length === 0) {
            ordersList.style.display = 'none';
            emptyState.style.display = 'block';
        } else {
            ordersList.style.display = 'block';
            emptyState.style.display = 'none';
        }
    }

    function checkAddressesEmpty() {
        const addressesContainer = document.querySelector('.addresses-container');
        const emptyState = document.querySelector('#addresses .empty-state');
        const addressCards = document.querySelectorAll('.address-card');

        if (addressCards.length === 0) {
            addressesContainer.style.display = 'none';
            emptyState.style.display = 'block';
        } else {
            addressesContainer.style.display = 'grid';
            emptyState.style.display = 'none';
        }
    }

    function checkWishlistEmpty() {
        const wishlistItems = document.querySelector('.wishlist-items');
        const emptyState = document.querySelector('#wishlist .empty-state');
        const productCards = document.querySelectorAll('.wishlist-product');

        if (productCards.length === 0) {
            wishlistItems.style.display = 'none';
            emptyState.style.display = 'block';
        } else {
            wishlistItems.style.display = 'grid';
            emptyState.style.display = 'none';
        }
    }

    // Initialize empty state checks
    checkEmptyStates('account-info');

    // Profile form submission
    const profileForm = document.querySelector('.profile-form');
    if (profileForm) {
        profileForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // Simulate form submission
            const submitButton = this.querySelector('button[type="submit"]');
            const originalText = submitButton.textContent;

            submitButton.textContent = 'Sauvegarde en cours...';
            submitButton.disabled = true;

            setTimeout(() => {
                submitButton.textContent = 'Sauvegardé!';

                setTimeout(() => {
                    submitButton.textContent = originalText;
                    submitButton.disabled = false;
                }, 2000);
            }, 1500);
        });
    }

    // Password form submission
    const passwordForm = document.querySelector('.password-form');
    if (passwordForm) {
        passwordForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const currentPassword = document.getElementById('current-password').value;
            const newPassword = document.getElementById('new-password').value;
            const confirmPassword = document.getElementById('confirm-password').value;

            // Simple validation
            if (!currentPassword || !newPassword || !confirmPassword) {
                alert('Veuillez remplir tous les champs');
                return;
            }

            if (newPassword !== confirmPassword) {
                alert('Les mots de passe ne correspondent pas');
                return;
            }

            // Simulate password change
            const submitButton = this.querySelector('button[type="submit"]');
            const originalText = submitButton.textContent;

            submitButton.textContent = 'Modification en cours...';
            submitButton.disabled = true;

            setTimeout(() => {
                submitButton.textContent = 'Mot de passe modifié!';

                // Clear form
                this.reset();

                setTimeout(() => {
                    submitButton.textContent = originalText;
                    submitButton.disabled = false;
                }, 2000);
            }, 1500);
        });
    }

    // Remove from wishlist
    const removeWishlistButtons = document.querySelectorAll('.remove-wishlist');
    removeWishlistButtons.forEach(button => {
        button.addEventListener('click', function() {
            const productCard = this.closest('.wishlist-product');

            if (confirm('Êtes-vous sûr de vouloir retirer cet article de votre liste de souhaits ?')) {
                productCard.style.opacity = '0';
                productCard.style.transform = 'scale(0.8)';

                setTimeout(() => {
                    productCard.remove();

                    // Check if wishlist is empty after removal
                    checkWishlistEmpty();
                }, 300);
            }
        });
    });

    // Add address buttons (both in empty state and add card)
    const addAddressButtons = document.querySelectorAll('.add-address-card button, .empty-state button');
    addAddressButtons.forEach(button => {
        if (button.textContent.includes('Ajouter')) {
            button.addEventListener('click', function() {
                alert('Fonctionnalité à venir: Ajouter une nouvelle adresse');
            });
        }
    });

    // Edit address buttons
    const editAddressButtons = document.querySelectorAll('.address-actions button:first-child');
    editAddressButtons.forEach(button => {
        button.addEventListener('click', function() {
            alert('Fonctionnalité à venir: Modifier l\'adresse');
        });
    });

    // Delete address buttons
    const deleteAddressButtons = document.querySelectorAll('.address-actions .btn-danger');
    deleteAddressButtons.forEach(button => {
        button.addEventListener('click', function() {
            if (confirm('Êtes-vous sûr de vouloir supprimer cette adresse?')) {
                const addressCard = this.closest('.address-card');

                addressCard.style.opacity = '0';
                addressCard.style.transform = 'scale(0.8)';

                setTimeout(() => {
                    addressCard.remove();

                    // Check if addresses section is empty after removal
                    checkAddressesEmpty();
                }, 300);
            }
        });
    });

    // Settings form
    const settingsActions = document.querySelector('.settings-actions');
    if (settingsActions) {
        const saveButton = settingsActions.querySelector('.btn');
        const resetButton = settingsActions.querySelector('.btn-secondary');

        if (saveButton) {
            saveButton.addEventListener('click', function(e) {
                e.preventDefault();

                // Simulate form submission
                const originalText = this.textContent;

                this.textContent = 'Sauvegarde en cours...';
                this.disabled = true;

                setTimeout(() => {
                    this.textContent = 'Paramètres sauvegardés!';

                    setTimeout(() => {
                        this.textContent = originalText;
                        this.disabled = false;
                    }, 2000);
                }, 1500);
            });
        }

        if (resetButton) {
            resetButton.addEventListener('click', function(e) {
                e.preventDefault();

                if (confirm('Êtes-vous sûr de vouloir réinitialiser tous les paramètres ?')) {
                    // Reset all form elements in settings
                    const checkboxes = document.querySelectorAll('#settings input[type="checkbox"]');
                    const selects = document.querySelectorAll('#settings select');

                    checkboxes.forEach(checkbox => {
                        checkbox.checked = false;
                    });

                    selects.forEach(select => {
                        select.selectedIndex = 0;
                    });

                    alert('Paramètres réinitialisés avec succès!');
                }
            });
        }
    }

    // Add smooth scrolling for better UX
    function smoothScrollToTop() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    }

    // Scroll to top when switching tabs
    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            setTimeout(smoothScrollToTop, 100);
        });
    });

    // Demo function to test empty states (for development purposes)
    window.testEmptyStates = function() {
        // Remove all orders to test empty state
        const orderItems = document.querySelectorAll('.order-item');
        orderItems.forEach(item => item.remove());
        checkOrdersEmpty();

        // Remove all addresses to test empty state
        const addressCards = document.querySelectorAll('.address-card');
        addressCards.forEach(card => card.remove());
        checkAddressesEmpty();

        // Remove all wishlist items to test empty state
        const wishlistProducts = document.querySelectorAll('.wishlist-product');
        wishlistProducts.forEach(product => product.remove());
        checkWishlistEmpty();

        console.log('Empty states activated for testing. Refresh page to restore content.');
    };
});
