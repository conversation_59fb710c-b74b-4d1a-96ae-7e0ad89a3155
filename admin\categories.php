<?php
require_once '../includes/config.php';

// Require admin access
$auth->requireAdmin();

$productObj = new Product();
$message = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add':
                $categoryData = [
                    'name' => $_POST['name'],
                    'slug' => strtolower(str_replace(' ', '-', $_POST['name'])),
                    'description' => $_POST['description']
                ];
                
                if ($productObj->createCategory($categoryData)) {
                    $message = 'Catégorie ajoutée avec succès.';
                } else {
                    $error = 'Erreur lors de l\'ajout de la catégorie.';
                }
                break;
                
            case 'edit':
                $categoryData = [
                    'name' => $_POST['name'],
                    'description' => $_POST['description']
                ];
                
                if ($productObj->updateCategory($_POST['category_id'], $categoryData)) {
                    $message = 'Catégorie modifiée avec succès.';
                } else {
                    $error = 'Erreur lors de la modification de la catégorie.';
                }
                break;
                
            case 'delete':
                if ($productObj->deleteCategory($_POST['category_id'])) {
                    $message = 'Catégorie supprimée avec succès.';
                } else {
                    $error = 'Erreur lors de la suppression de la catégorie.';
                }
                break;
        }
    }
}

// Get all categories
$categories = $productObj->getAllCategories();

// Get category for editing if specified
$editCategory = null;
if (isset($_GET['edit'])) {
    foreach ($categories as $category) {
        if ($category['id'] == $_GET['edit']) {
            $editCategory = $category;
            break;
        }
    }
}

$pageTitle = 'Gestion des Catégories';
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $pageTitle ?> - <?= SITE_NAME ?></title>
    <link rel="stylesheet" href="../css/style.css">
    <style>
        .admin-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        
        .admin-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }
        
        .btn-admin {
            display: inline-block;
            padding: 12px 24px;
            background: var(--primary-color);
            color: var(--secondary-color);
            text-decoration: none;
            border: none;
            cursor: pointer;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .btn-admin:hover {
            background: #333;
        }
        
        .btn-small {
            padding: 8px 16px;
            font-size: 12px;
            margin-right: 5px;
        }
        
        .btn-danger {
            background: #dc3545;
        }
        
        .btn-danger:hover {
            background: #c82333;
        }
        
        .message {
            padding: 15px;
            margin-bottom: 20px;
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .categories-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 40px;
        }
        
        .categories-table th,
        .categories-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e0e0e0;
        }
        
        .categories-table th {
            background: #f8f8f8;
            font-weight: bold;
        }
        
        .form-container {
            background: #f8f8f8;
            padding: 30px;
            margin-bottom: 40px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            font-family: inherit;
        }
        
        .form-group textarea {
            height: 100px;
            resize: vertical;
        }
    </style>
</head>
<body class="admin-page">
    <?php include '../includes/header.php'; ?>

    <main>
        <div class="admin-container">
            <div class="admin-header">
                <h1><?= $pageTitle ?></h1>
                <a href="dashboard.php" class="btn-admin">Retour au tableau de bord</a>
            </div>

            <?php if ($message): ?>
                <div class="message"><?= $message ?></div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="message error"><?= $error ?></div>
            <?php endif; ?>

            <!-- Category Form -->
            <div class="form-container">
                <h2><?= $editCategory ? 'Modifier la catégorie' : 'Ajouter une nouvelle catégorie' ?></h2>
                <form method="POST">
                    <input type="hidden" name="action" value="<?= $editCategory ? 'edit' : 'add' ?>">
                    <?php if ($editCategory): ?>
                        <input type="hidden" name="category_id" value="<?= $editCategory['id'] ?>">
                    <?php endif; ?>
                    
                    <div class="form-group">
                        <label for="name">Nom de la catégorie</label>
                        <input type="text" id="name" name="name" value="<?= $editCategory['name'] ?? '' ?>" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="description">Description</label>
                        <textarea id="description" name="description"><?= $editCategory['description'] ?? '' ?></textarea>
                    </div>
                    
                    <button type="submit" class="btn-admin">
                        <?= $editCategory ? 'Modifier la catégorie' : 'Ajouter la catégorie' ?>
                    </button>
                    
                    <?php if ($editCategory): ?>
                        <a href="categories.php" class="btn-admin" style="background: #6c757d; margin-left: 10px;">Annuler</a>
                    <?php endif; ?>
                </form>
            </div>

            <!-- Categories List -->
            <h2>Liste des catégories</h2>
            <table class="categories-table">
                <thead>
                    <tr>
                        <th>Nom</th>
                        <th>Slug</th>
                        <th>Description</th>
                        <th>Nombre de produits</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($categories as $category): ?>
                        <?php
                        // Count products in this category
                        $productCount = count($productObj->getByCategory($category['slug']));
                        ?>
                        <tr>
                            <td><?= $category['name'] ?></td>
                            <td><?= $category['slug'] ?></td>
                            <td><?= $category['description'] ?></td>
                            <td><?= $productCount ?></td>
                            <td>
                                <a href="categories.php?edit=<?= $category['id'] ?>" class="btn-admin btn-small">Modifier</a>
                                <?php if ($productCount == 0): ?>
                                    <form method="POST" style="display: inline;" onsubmit="return confirm('Êtes-vous sûr de vouloir supprimer cette catégorie ?')">
                                        <input type="hidden" name="action" value="delete">
                                        <input type="hidden" name="category_id" value="<?= $category['id'] ?>">
                                        <button type="submit" class="btn-admin btn-small btn-danger">Supprimer</button>
                                    </form>
                                <?php else: ?>
                                    <span style="color: #666; font-size: 12px;">Contient des produits</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </main>

    <?php include '../includes/footer.php'; ?>
    
    <script src="../js/main.js"></script>
</body>
</html>
