/**
 * Nuit Blanche - Shop Page JavaScript
 */

document.addEventListener('DOMContentLoaded', function() {
    // Collection item hover effect
    const collectionItems = document.querySelectorAll('.collection-item');
    
    collectionItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.querySelector('img').style.transform = 'scale(1.05)';
        });
        
        item.addEventListener('mouseleave', function() {
            this.querySelector('img').style.transform = 'scale(1)';
        });
    });
    
    // Filter functionality (placeholder)
    const filterButtons = document.querySelectorAll('.filter-button');
    if (filterButtons.length > 0) {
        filterButtons.forEach(button => {
            button.addEventListener('click', function() {
                const filter = this.dataset.filter;
                console.log(`Filter selected: ${filter}`);
                // Implement actual filtering logic here
            });
        });
    }
    
    // Quick view functionality (placeholder)
    const quickViewButtons = document.querySelectorAll('.quick-view-button');
    if (quickViewButtons.length > 0) {
        quickViewButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const productId = this.dataset.productId;
                console.log(`Quick view for product ID: ${productId}`);
                // Implement actual quick view logic here
                alert('Aperçu rapide - Fonctionnalité à venir');
            });
        });
    }
});
